using UnityEngine;


[System.Serializable]
public class BodyType
{
    public Mesh[] meshes;
}

public class Custumizer : MonoBehaviour
{
    public CustomPreset skin;
    public BodyType[] body;
    public GameObject[] hair;
    public GameObject[] shirt;
    public GameObject[] pants;
    public GameObject[] shoes;
    public GameObject[] accessory;
    void Start()
    {
        if (skin == null)
        {
            skin = new CustomPreset();
        }

        Apply();
    }


    public void Apply()
    {
        transform.GetChild(0).GetChild(1).GetComponent<SkinnedMeshRenderer>().sharedMesh = body[skin.body].meshes[skin.bodyColor];
        /*hair[skin.hair].SetActive(true);
        if (skin.shirt[0] != -1)
        {
            shirt[skin.shirt[0]].SetActive(true);
        }
        if (skin.shirt[1] != -1)
        {
            shirt[skin.shirt[1]].SetActive(true);
        }
        pants[skin.pants].SetActive(true);
        shoes[skin.shoes].SetActive(true);
        if (skin.accessory[0] != -1)
        {
            accessory[skin.accessory[0]].SetActive(true);
        }
        if (skin.accessory[1] != -1)
        {
            accessory[skin.accessory[1]].SetActive(true);
        }*/
    }

}

[System.Serializable]
public class CustomPreset
{
    public int body;
    public int bodyColor;
    public int hair;
    public int[] shirt;
    public int pants;
    public int shoes;
    public int[] accessory;

    public CustomPreset(int body, int bodyColor, int hair, int[] shirt, int pants, int shoes, int[] accessory)
    {
        this.body = body;
        this.bodyColor = bodyColor;
        this.hair = hair;
        this.shirt = shirt;
        this.pants = pants;
        this.shoes = shoes;
        this.accessory = accessory;
    }

    public CustomPreset()
    {
        this.body = 0;
        this.bodyColor = 0;
        this.hair = 0;
        this.shirt = new int[2] { 0, -1 };
        this.pants = 0;
        this.shoes = 0;
        this.accessory = new int[2] { 0, -1 };
    }
    

}
