Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.47f1 (2ad1ed33fd3b) revision 2806253'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'fr' Physical Memory: 16051 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker50
-projectPath
C:/Users/<USER>/ONU
-logFile
Logs/AssetImportWorker50.log
-srvPort
56204
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: C:/Users/<USER>/ONU
C:/Users/<USER>/ONU
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [62408]  Target information:

Player connection [62408]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 3424610443 [EditorId] 3424610443 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-34E5P9N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [62408] Host joined multi-casting on [***********:54997]...
Player connection [62408] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 2.05 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.47f1 (2ad1ed33fd3b)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/ONU/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1650 (ID=0x1f9d)
    Vendor:   NVIDIA
    VRAM:     3937 MB
    Driver:   32.0.15.7602
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56228
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.002814 seconds.
- Loaded All Assemblies, in  0.327 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 227 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.533 seconds
Domain Reload Profiling: 859ms
	BeginReloadAssembly (113ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (41ms)
	LoadAllAssembliesAndSetupDomain (133ms)
		LoadAssemblies (111ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (131ms)
			TypeCache.Refresh (129ms)
				TypeCache.ScanAssembly (118ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (534ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (491ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (304ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (41ms)
			ProcessInitializeOnLoadAttributes (94ms)
			ProcessInitializeOnLoadMethodAttributes (49ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.756 seconds
Refreshing native plugins compatible for Editor in 1.55 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mirror | mirror-networking.com | discord.gg/N9QVxbM
Mono: successfully reloaded assembly
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.873 seconds
Domain Reload Profiling: 1627ms
	BeginReloadAssembly (140ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (24ms)
	RebuildCommonClasses (43ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (35ms)
	LoadAllAssembliesAndSetupDomain (522ms)
		LoadAssemblies (344ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (261ms)
			TypeCache.Refresh (193ms)
				TypeCache.ScanAssembly (174ms)
			BuildScriptInfoCaches (52ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (874ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (721ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (115ms)
			ProcessInitializeOnLoadAttributes (343ms)
			ProcessInitializeOnLoadMethodAttributes (244ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 3.26 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 219 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7357 unused Assets / (7.3 MB). Loaded Objects now: 8019.
Memory consumption went from 185.1 MB to 177.8 MB.
Total: 14.838400 ms (FindLiveObjects: 0.767600 ms CreateObjectMapping: 1.033100 ms MarkObjects: 7.564700 ms  DeleteObjects: 5.468100 ms)

========================================================================
Received Import Request.
  Time since last request: 1840025.048579 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Armchair_01B.prefab
  artifactKey: Guid(65ccd8c85e9c8c848b72a6a7a375b516) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Armchair_01B.prefab using Guid(65ccd8c85e9c8c848b72a6a7a375b516) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '084cda925921298677a7b60dfe35ab64') in 0.6001227 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Dresser_01A.prefab
  artifactKey: Guid(1259a87bde1269246992f8c4f7ba7a5e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Dresser_01A.prefab using Guid(1259a87bde1269246992f8c4f7ba7a5e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cf0d9ffe32039bc3b6dfab7309adddee') in 0.0295845 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 22

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Books_01A.prefab
  artifactKey: Guid(991315637833d114bbf1c0978bfb4680) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Books_01A.prefab using Guid(991315637833d114bbf1c0978bfb4680) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '185a6f5e37366dd0b6fc5ee49676a5f3') in 0.0265773 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Book_01B.prefab
  artifactKey: Guid(06d8f78978a0ff84a9b505ec22afa2ce) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Book_01B.prefab using Guid(06d8f78978a0ff84a9b505ec22afa2ce) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '31063e293d403bba912b1fef173b7e75') in 0.0243611 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Coffee_Table_01A.prefab
  artifactKey: Guid(4b593440963e9ff48a1f1c96591b3fe3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Coffee_Table_01A.prefab using Guid(4b593440963e9ff48a1f1c96591b3fe3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '408b301549fafab357a0ecf02011ed01') in 0.0443102 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Bookshelf_02A.prefab
  artifactKey: Guid(1d7ba7fa294c0844fb9a79ebd014fb7b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Bookshelf_02A.prefab using Guid(1d7ba7fa294c0844fb9a79ebd014fb7b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'dc6d8acdb0224746514a8f2c79785d29') in 0.0285869 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Book_02B.prefab
  artifactKey: Guid(85024937a8b2eaf489019b1a290069fe) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Book_02B.prefab using Guid(85024937a8b2eaf489019b1a290069fe) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b0a6a4396cabad0c57773ef16f684948') in 0.0236661 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Couch_02C.prefab
  artifactKey: Guid(80517b9e52fe92340b4ae78f40bd45d0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Couch_02C.prefab using Guid(80517b9e52fe92340b4ae78f40bd45d0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6ce506da60b0c32942fca411181815b5') in 0.0259039 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Armchair_01C.prefab
  artifactKey: Guid(3978ba5b1b91cf34ba3afe4d089b3e7c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Armchair_01C.prefab using Guid(3978ba5b1b91cf34ba3afe4d089b3e7c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '557c2d2a0f2eaa48b19d792d04a1bad5') in 0.0213801 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Fireplace_01A.prefab
  artifactKey: Guid(30cf86ba8be2afc438ee3e9764daf056) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Fireplace_01A.prefab using Guid(30cf86ba8be2afc438ee3e9764daf056) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f6a8286e64784b5cd696136aac0c1516') in 0.0226035 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Bed_02B.prefab
  artifactKey: Guid(29e6109b1080ab64cb724991d746483b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Bed_02B.prefab using Guid(29e6109b1080ab64cb724991d746483b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8df0eaaa92b5f7696ff180b1009e3f1c') in 0.0255194 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Dryer_01A.prefab
  artifactKey: Guid(665c0ce2818c6284f81d1999e71bdd6d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Dryer_01A.prefab using Guid(665c0ce2818c6284f81d1999e71bdd6d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '70c0ca2cc5dd00aa3dd0ce246e907e6d') in 0.0229699 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Bed_01B.prefab
  artifactKey: Guid(688f46c06914f124fa54f7f7ccc2dc9b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Bed_01B.prefab using Guid(688f46c06914f124fa54f7f7ccc2dc9b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7b50f9870877a5c6332a2502d2f81682') in 0.0230435 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Armchair_02A.prefab
  artifactKey: Guid(bfbd85aa930d8034cad928b9225ccd8c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Armchair_02A.prefab using Guid(bfbd85aa930d8034cad928b9225ccd8c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '30e2747437955448ca7612a0454bb99d') in 0.0227291 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Ceiling_Fixture_03B.prefab
  artifactKey: Guid(04d33466aed021042b07f7e8cfb6c1a5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Ceiling_Fixture_03B.prefab using Guid(04d33466aed021042b07f7e8cfb6c1a5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4dbf52194f8bda73216b0ce543b73df9') in 0.0343006 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 21

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Ceiling_Fixture_01A.prefab
  artifactKey: Guid(6a9814642f648354698cc7c23da350ed) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Ceiling_Fixture_01A.prefab using Guid(6a9814642f648354698cc7c23da350ed) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '56dee04609745d6eb49ecb4626fd3778') in 0.0237257 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Bed_01A.prefab
  artifactKey: Guid(d267d9f2f4d712c44bd7b1fdc236d040) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Bed_01A.prefab using Guid(d267d9f2f4d712c44bd7b1fdc236d040) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8a9394ae891e069472313c7d481f7b30') in 0.0253749 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Bowl_01B.prefab
  artifactKey: Guid(ee4b325e6c2d8df4db5ca6688ad080a2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Bowl_01B.prefab using Guid(ee4b325e6c2d8df4db5ca6688ad080a2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5381de38bd17ee631f6b7d3fe4355afb') in 0.0235464 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Ceiling_Fixture_03A.prefab
  artifactKey: Guid(76fa76b8ae56a6542befccb4019e2062) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Ceiling_Fixture_03A.prefab using Guid(76fa76b8ae56a6542befccb4019e2062) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2f6bbcee66c6ac7c8f769adba514fa34') in 0.0254174 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 21

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Desk_01A.prefab
  artifactKey: Guid(080e2fe96dd732442a3321c550fbd054) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Desk_01A.prefab using Guid(080e2fe96dd732442a3321c550fbd054) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bbf2860c7cf5e1ca93f8e7733d3efe79') in 0.020579 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Book_02A.prefab
  artifactKey: Guid(53ee4b15ab70a2b438278f468937ec3a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Book_02A.prefab using Guid(53ee4b15ab70a2b438278f468937ec3a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '26dd6af95d4f4fdf99f4e21df1d9c984') in 0.0215039 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Bowl_02D.prefab
  artifactKey: Guid(d2163034f53661c42a96932624576c7e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Bowl_02D.prefab using Guid(d2163034f53661c42a96932624576c7e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fa301ee85c23662639a26460a3de25dd') in 0.0323112 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Chandelier_01A.prefab
  artifactKey: Guid(08f3582c690c46b47a5913d9bb0587bd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Chandelier_01A.prefab using Guid(08f3582c690c46b47a5913d9bb0587bd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fa5bf2ff18bedf13421a0bf2dd1ce353') in 0.0238773 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 0.000102 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Ceiling_Fixture_02A.prefab
  artifactKey: Guid(26e33f1fedc4a7742be730d2755c5c26) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Ceiling_Fixture_02A.prefab using Guid(26e33f1fedc4a7742be730d2755c5c26) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b1d6aad0f03619879b81ec024bc015cb') in 0.0238078 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 1.658405 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Lamp_01B.prefab
  artifactKey: Guid(a1f4d326a8ba07b419a49587e51cb3dc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Lamp_01B.prefab using Guid(a1f4d326a8ba07b419a49587e51cb3dc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'acdd308699a3a690d4e045aefe0b07d3') in 0.0273491 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Nightstand_02A.prefab
  artifactKey: Guid(23aa79e77416bf3499a58e0cf8291564) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Nightstand_02A.prefab using Guid(23aa79e77416bf3499a58e0cf8291564) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '63d08c3238b785e4a10cb0a2dd71d026') in 0.0270969 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 17

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Nightstand_01B.prefab
  artifactKey: Guid(9c6d7a1c1457a94428c28f4678552e86) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Nightstand_01B.prefab using Guid(9c6d7a1c1457a94428c28f4678552e86) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '00dbbcd3de95e96e6b475d239c5c05e0') in 0.0284599 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Ottoman_01B.prefab
  artifactKey: Guid(4185bd509a486a045a32582ffe23cc62) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Ottoman_01B.prefab using Guid(4185bd509a486a045a32582ffe23cc62) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c353f11989641adc310aea066d21947f') in 0.0225067 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Ottoman_01C.prefab
  artifactKey: Guid(c4670d8ad1521c143a9f2e27e2495dab) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Ottoman_01C.prefab using Guid(c4670d8ad1521c143a9f2e27e2495dab) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5f239ff59d9bb11bc6f15adfe9b5f75f') in 0.0217416 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Light_Switch_01A.prefab
  artifactKey: Guid(7fb98ce35aea8b347a76393b0a93113a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Light_Switch_01A.prefab using Guid(7fb98ce35aea8b347a76393b0a93113a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fa8d390f9ac795a5d9d03b0d93c7d0fb') in 0.0216432 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Lounge_Chair_02B.prefab
  artifactKey: Guid(89767dc0e032de64a9a7428516482988) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Lounge_Chair_02B.prefab using Guid(89767dc0e032de64a9a7428516482988) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '547768f8800777a51024cfb087b0b071') in 0.023069 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Lounge_Chair_02A.prefab
  artifactKey: Guid(05ea28b8dcdf5414da2b616e907664f2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Lounge_Chair_02A.prefab using Guid(05ea28b8dcdf5414da2b616e907664f2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'af73ba858ed30935943144905ffe1a33') in 0.021025 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 266.893822 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_PC_Keyboard_01A.prefab
  artifactKey: Guid(d8d543de319c1124e94fd2ba708d604e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_PC_Keyboard_01A.prefab using Guid(d8d543de319c1124e94fd2ba708d604e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f8ee8e8844fb7a84ef1c91f4ef3312c8') in 0.0286708 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_PC_Mouse_01A.prefab
  artifactKey: Guid(8c058c71787183a48994914f323540fa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_PC_Mouse_01A.prefab using Guid(8c058c71787183a48994914f323540fa) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ab4141df9b6be6ca3d1f930e1e8d1e25') in 0.0372803 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_PC_Screen_01A.prefab
  artifactKey: Guid(4f0f62e894c84464db567f2f38cce3ba) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_PC_Screen_01A.prefab using Guid(4f0f62e894c84464db567f2f38cce3ba) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5389ebe8f19614034272c167cdc21e21') in 0.0283855 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Pillow_01C.prefab
  artifactKey: Guid(b715fcbbd66067945bba12afe8e9e936) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Pillow_01C.prefab using Guid(b715fcbbd66067945bba12afe8e9e936) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '43672b884fd5bd84199721d295a9fff2') in 0.0340256 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.038114 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Pillow_01E.prefab
  artifactKey: Guid(bc8afaf13197a644687a6d82159c047e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Pillow_01E.prefab using Guid(bc8afaf13197a644687a6d82159c047e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '19e4b5fddfe6ce38fd1797ac538e0e84') in 0.0220485 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Pillow_01I.prefab
  artifactKey: Guid(c4d410e1c007427478738b7332069923) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Pillow_01I.prefab using Guid(c4d410e1c007427478738b7332069923) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c5f198e87b3eed09317c29cedd3af67c') in 0.0232979 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Power_Outlet_01A.prefab
  artifactKey: Guid(a6448c9dcb56a4f45b2986a78b1f3870) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Power_Outlet_01A.prefab using Guid(a6448c9dcb56a4f45b2986a78b1f3870) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2f5b68d8093c633fcc6deafedfe309b4') in 0.02318 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Pillow_01G.prefab
  artifactKey: Guid(fcb410f43352cf849ba5b7f382093d4b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Pillow_01G.prefab using Guid(fcb410f43352cf849ba5b7f382093d4b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '18f3c0921186bd8dff1a17cfaddeaa4b') in 0.0222998 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.175921 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_01C.prefab
  artifactKey: Guid(c714e46713b40d64b9c4862e1d26ab51) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_01C.prefab using Guid(c714e46713b40d64b9c4862e1d26ab51) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '116bd5b9f015377329f205e6b121828a') in 0.0274465 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_03B.prefab
  artifactKey: Guid(2a781db12618fd3409e4705bf58bfcfa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_03B.prefab using Guid(2a781db12618fd3409e4705bf58bfcfa) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '489e5509cb06b33590af1139572c9ad2') in 0.0234001 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_03A.prefab
  artifactKey: Guid(550a3ca4f8e41a9489af5b64e9a22162) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_03A.prefab using Guid(550a3ca4f8e41a9489af5b64e9a22162) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2917bad3b8be2a04037fcd7f841f544b') in 0.0228575 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_04A.prefab
  artifactKey: Guid(197096e2bdb80bb43b93c9f6dd2d911b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_04A.prefab using Guid(197096e2bdb80bb43b93c9f6dd2d911b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'eb76a2338a5adfdf27ac97bc4939a78c') in 0.0235105 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0