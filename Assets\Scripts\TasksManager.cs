using UnityEngine;

public class TasksManager : MonoBehaviour
{
    public Tasks[] tasks;
    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        LoadDataFromChildren();
    }

    // Update is called once per frame
    void Update()
    {

    }

    public void LoadDataFromChildren()
    {
        if (transform.childCount == 0)
        {
            Debug.LogError("No children found");
            return;
        }
        tasks = new Tasks[transform.childCount];
        foreach (Transform child in transform)
        {
            tasks[child.GetSiblingIndex()] = child.GetComponent<Tasks>();
        }
    }
}
