using UnityEngine;

[System.Serializable]
public enum TaskType
{





    Quiz,


    SpeedQuiz,


    Duel









}
public class Tasks : MonoBehaviour
{
    public static int taskCount = 0;
    private BuildingManager buildingManager;
    public int buildingIndex;
    public TaskType taskType;
    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        buildingManager = FindFirstObjectByType<BuildingManager>();
    }

    // Update is called once per frame
    void Update()
    {

    }
    public void CompleteTask()
    {
        buildingManager.buildings[buildingIndex].NextLevel();
        taskCount++;
        Debug.Log("Task Number " + taskCount + " completed");
        buildingManager.buildings[0].SetLevel((int)(taskCount / 5));
        Destroy(gameObject);
    }


}
