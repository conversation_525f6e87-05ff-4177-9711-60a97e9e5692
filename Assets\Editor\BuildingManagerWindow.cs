using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.Linq;

public class BuildingManagerWindow : EditorWindow
{
    private BuildingManager buildingManager;
    private BuildingManager[] allBuildingManagers;
    private int selectedManagerIndex = 0;
    private string[] managerNames;
    private Vector2 scrollPosition;
    private int selectedBuildingIndex = -1;
    private string searchFilter = "";
    private bool showBuildingDetails = true;
    private bool showBuildingList = true;
    private bool enableHighlighting = true;

    // Building editing fields
    private string editingName = "";
    private string editingType = "";
    private string editingDescription = "";
    private int editingLevel = 0;
    private int editingMaxLevel = 1;

    [MenuItem("Tools/Building Manager")]
    public static void ShowWindow()
    {
        BuildingManagerWindow window = GetWindow<BuildingManagerWindow>("Building Manager");
        window.minSize = new Vector2(400, 300);
        window.Show();
    }

    void OnEnable()
    {
        FindBuildingManager();

        // Subscribe to selection changes
        Selection.selectionChanged += OnSelectionChanged;

        // Subscribe to scene changes
        EditorApplication.hierarchyChanged += OnHierarchyChanged;

        // Enable building highlighting
        BuildingHighlighter.SetEnabled(enableHighlighting);
    }

    void OnDisable()
    {
        // Unsubscribe from selection changes
        Selection.selectionChanged -= OnSelectionChanged;

        // Unsubscribe from scene changes
        EditorApplication.hierarchyChanged -= OnHierarchyChanged;
    }

    void OnGUI()
    {
        if (buildingManager == null)
        {
            DrawNoBuildingManagerGUI();
            return;
        }

        DrawToolbar();
        
        EditorGUILayout.BeginHorizontal();
        
        // Left panel - Building list
        if (showBuildingList)
        {
            DrawBuildingList();
        }
        
        // Right panel - Building details
        if (showBuildingDetails)
        {
            DrawBuildingDetails();
        }
        
        EditorGUILayout.EndHorizontal();
    }

    private void FindBuildingManager()
    {
        // Find all BuildingManager instances in the scene
        allBuildingManagers = FindObjectsByType<BuildingManager>(FindObjectsSortMode.None);

        if (allBuildingManagers != null && allBuildingManagers.Length > 0)
        {
            // Create display names for the dropdown
            managerNames = new string[allBuildingManagers.Length];
            for (int i = 0; i < allBuildingManagers.Length; i++)
            {
                string managerName = allBuildingManagers[i].name;
                if (string.IsNullOrEmpty(managerName))
                    managerName = $"BuildingManager {i + 1}";
                managerNames[i] = managerName;
            }

            // Ensure selected index is valid
            if (selectedManagerIndex >= allBuildingManagers.Length)
                selectedManagerIndex = 0;

            // Set the current building manager
            SetCurrentBuildingManager(selectedManagerIndex);
        }
        else
        {
            buildingManager = null;
            allBuildingManagers = null;
            managerNames = null;
        }
    }

    private void SetCurrentBuildingManager(int index)
    {
        // Unsubscribe from previous manager events
        if (buildingManager != null)
        {
            buildingManager.OnBuildingAdded -= OnBuildingChanged;
            buildingManager.OnBuildingRemoved -= OnBuildingChanged;
            buildingManager.OnBuildingUpdated -= OnBuildingChanged;
            buildingManager.OnBuildingsRefreshed -= OnBuildingsRefreshed;
        }

        // Set new manager
        if (allBuildingManagers != null && index >= 0 && index < allBuildingManagers.Length)
        {
            buildingManager = allBuildingManagers[index];
            selectedManagerIndex = index;

            // Subscribe to new manager events
            buildingManager.OnBuildingAdded += OnBuildingChanged;
            buildingManager.OnBuildingRemoved += OnBuildingChanged;
            buildingManager.OnBuildingUpdated += OnBuildingChanged;
            buildingManager.OnBuildingsRefreshed += OnBuildingsRefreshed;

            // Reset selection when switching managers
            selectedBuildingIndex = -1;

            // Clear highlights when switching managers
            if (enableHighlighting)
            {
                BuildingHighlighter.ClearHighlights();
            }
        }
        else
        {
            buildingManager = null;
        }
    }

    private void OnDestroy()
    {
        if (buildingManager != null)
        {
            // Unsubscribe from events
            buildingManager.OnBuildingAdded -= OnBuildingChanged;
            buildingManager.OnBuildingRemoved -= OnBuildingChanged;
            buildingManager.OnBuildingUpdated -= OnBuildingChanged;
            buildingManager.OnBuildingsRefreshed -= OnBuildingsRefreshed;
        }
    }

    private void OnBuildingChanged(BuildingData data)
    {
        Repaint();
    }

    private void OnBuildingsRefreshed()
    {
        Repaint();
    }

    private void OnSelectionChanged()
    {
        if (buildingManager == null) return;

        // Check if selected object is a building
        if (Selection.activeGameObject != null)
        {
            Building selectedBuilding = Selection.activeGameObject.GetComponent<Building>();
            if (selectedBuilding != null)
            {
                // Find the building in our list and select it
                BuildingData buildingData = buildingManager.GetBuildingData(selectedBuilding);
                if (buildingData != null)
                {
                    List<BuildingData> allBuildings = buildingManager.GetAllBuildingData();
                    selectedBuildingIndex = allBuildings.IndexOf(buildingData);
                    LoadBuildingForEditing(buildingData);
                    Repaint();
                }
            }
        }
    }

    private void OnHierarchyChanged()
    {
        // Refresh the list of building managers
        BuildingManager[] previousManagers = allBuildingManagers;
        FindBuildingManager();

        // If the number of managers changed or the current manager is no longer valid, refresh
        if (previousManagers == null ||
            allBuildingManagers == null ||
            previousManagers.Length != allBuildingManagers.Length ||
            buildingManager == null ||
            (allBuildingManagers.Length > 0 && !System.Array.Exists(allBuildingManagers, m => m == buildingManager)))
        {
            Repaint();
        }

        // Refresh buildings in current manager when hierarchy changes
        if (buildingManager != null)
        {
            buildingManager.RefreshBuildings();
        }
    }

    private void DrawNoBuildingManagerGUI()
    {
        EditorGUILayout.HelpBox("No BuildingManager found in the scene. Please add a BuildingManager component to a GameObject.", MessageType.Warning);

        if (GUILayout.Button("Create BuildingManager"))
        {
            GameObject go = new GameObject("BuildingManager");
            go.AddComponent<BuildingManager>();
            Selection.activeGameObject = go;

            // Refresh the manager list after creating a new one
            FindBuildingManager();
        }

        if (GUILayout.Button("Find BuildingManager"))
        {
            FindBuildingManager();
        }
    }

    private void DrawToolbar()
    {
        EditorGUILayout.BeginHorizontal(EditorStyles.toolbar);

        // BuildingManager selection dropdown
        if (allBuildingManagers != null && allBuildingManagers.Length > 1)
        {
            EditorGUILayout.LabelField("Manager:", GUILayout.Width(60));
            int newSelectedIndex = EditorGUILayout.Popup(selectedManagerIndex, managerNames, EditorStyles.toolbarPopup, GUILayout.Width(150));
            if (newSelectedIndex != selectedManagerIndex)
            {
                SetCurrentBuildingManager(newSelectedIndex);
            }
            GUILayout.Space(10);
        }
        else if (allBuildingManagers != null && allBuildingManagers.Length == 1)
        {
            EditorGUILayout.LabelField($"Manager: {managerNames[0]}", GUILayout.Width(150));
            GUILayout.Space(10);
        }

        if (GUILayout.Button("Refresh", EditorStyles.toolbarButton))
        {
            if (buildingManager != null)
                buildingManager.RefreshBuildings();
        }

        GUILayout.Space(10);

        if (GUILayout.Button("Save Data", EditorStyles.toolbarButton))
        {
            if (buildingManager != null)
                buildingManager.SaveBuildingData();
        }

        if (GUILayout.Button("Load Data", EditorStyles.toolbarButton))
        {
            if (buildingManager != null)
                buildingManager.LoadBuildingData();
        }

        GUILayout.FlexibleSpace();

        showBuildingList = GUILayout.Toggle(showBuildingList, "List", EditorStyles.toolbarButton);
        showBuildingDetails = GUILayout.Toggle(showBuildingDetails, "Details", EditorStyles.toolbarButton);

        bool newHighlighting = GUILayout.Toggle(enableHighlighting, "Highlight", EditorStyles.toolbarButton);
        if (newHighlighting != enableHighlighting)
        {
            enableHighlighting = newHighlighting;
            BuildingHighlighter.SetEnabled(enableHighlighting);
        }

        EditorGUILayout.EndHorizontal();
    }

    private void DrawBuildingList()
    {
        EditorGUILayout.BeginVertical(GUILayout.Width(200));
        
        // Search filter
        EditorGUILayout.LabelField("Buildings", EditorStyles.boldLabel);
        searchFilter = EditorGUILayout.TextField("Search", searchFilter);
        
        // Building list
        scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
        
        List<BuildingData> buildings = buildingManager.GetAllBuildingData();
        List<BuildingData> filteredBuildings = buildings;
        
        if (!string.IsNullOrEmpty(searchFilter))
        {
            filteredBuildings = buildings.Where(b => 
                b.buildingName.ToLower().Contains(searchFilter.ToLower()) ||
                b.buildingType.ToLower().Contains(searchFilter.ToLower())
            ).ToList();
        }
        
        for (int i = 0; i < filteredBuildings.Count; i++)
        {
            BuildingData building = filteredBuildings[i];
            bool isSelected = selectedBuildingIndex == buildings.IndexOf(building);
            
            if (DrawBuildingListItem(building, isSelected))
            {
                selectedBuildingIndex = buildings.IndexOf(building);
                LoadBuildingForEditing(building);

                // Select the building in the scene
                Building buildingComponent = buildingManager.GetBuilding(building.id);
                if (buildingComponent != null)
                {
                    Selection.activeGameObject = buildingComponent.gameObject;
                    SceneView.FrameLastActiveSceneView();

                    // Highlight the building
                    if (enableHighlighting)
                    {
                        BuildingHighlighter.ClearHighlights();
                        BuildingHighlighter.HighlightBuilding(buildingComponent);
                    }
                }
            }
        }
        
        EditorGUILayout.EndScrollView();

        // Add building creation controls at the bottom of the list
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("Create New", EditorStyles.boldLabel);

        if (GUILayout.Button("Create from Prefab"))
        {
            CreateBuildingFromPrefab();
        }

        if (GUILayout.Button("Create Empty"))
        {
            CreateEmptyBuilding();
        }

        EditorGUILayout.EndVertical();
    }

    private bool DrawBuildingListItem(BuildingData building, bool isSelected)
    {
        Color originalColor = GUI.backgroundColor;
        if (isSelected)
        {
            GUI.backgroundColor = Color.cyan;
        }
        
        EditorGUILayout.BeginVertical(EditorStyles.helpBox);
        
        bool clicked = false;
        if (GUILayout.Button("", GUIStyle.none, GUILayout.Height(0)))
        {
            clicked = true;
        }
        
        EditorGUILayout.LabelField(building.buildingName, EditorStyles.boldLabel);
        EditorGUILayout.LabelField($"Type: {building.buildingType}", EditorStyles.miniLabel);
        EditorGUILayout.LabelField($"Level: {building.currentLevel + 1}/{building.maxLevel}", EditorStyles.miniLabel);
        
        if (GUILayout.Button(building.buildingName, EditorStyles.label))
        {
            clicked = true;
        }
        
        EditorGUILayout.EndVertical();
        
        GUI.backgroundColor = originalColor;
        return clicked;
    }

    private void LoadBuildingForEditing(BuildingData building)
    {
        editingName = building.buildingName;
        editingType = building.buildingType;
        editingDescription = building.description;
        editingLevel = building.currentLevel;
        editingMaxLevel = building.maxLevel;
    }

    private void DrawBuildingDetails()
    {
        EditorGUILayout.BeginVertical();
        
        if (selectedBuildingIndex >= 0 && selectedBuildingIndex < buildingManager.GetAllBuildingData().Count)
        {
            BuildingData selectedBuilding = buildingManager.GetAllBuildingData()[selectedBuildingIndex];
            DrawSelectedBuildingDetails(selectedBuilding);
        }
        else
        {
            EditorGUILayout.HelpBox("Select a building from the list to view and edit its details.", MessageType.Info);
        }
        
        EditorGUILayout.EndVertical();
    }

    private void DrawSelectedBuildingDetails(BuildingData building)
    {
        EditorGUILayout.LabelField("Building Details", EditorStyles.boldLabel);
        
        EditorGUILayout.Space();
        
        // Building name
        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField("Name:", GUILayout.Width(80));
        string newName = EditorGUILayout.TextField(editingName);
        if (newName != editingName)
        {
            editingName = newName;
            buildingManager.RenameBuilding(building.id, newName);
        }
        EditorGUILayout.EndHorizontal();
        
        // Building type
        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField("Type:", GUILayout.Width(80));
        string newType = EditorGUILayout.TextField(editingType);
        if (newType != editingType)
        {
            editingType = newType;
            buildingManager.SetBuildingType(building.id, newType);
        }
        EditorGUILayout.EndHorizontal();
        
        // Building level controls
        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField("Level:", GUILayout.Width(80));
        EditorGUILayout.LabelField($"{building.currentLevel + 1} / {building.maxLevel}");
        EditorGUILayout.EndHorizontal();
        
        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("Previous Level") && building.currentLevel > 0)
        {
            buildingManager.LevelDownBuilding(building.id);
        }
        if (GUILayout.Button("Next Level") && building.currentLevel < building.maxLevel - 1)
        {
            buildingManager.LevelUpBuilding(building.id);
        }
        EditorGUILayout.EndHorizontal();
        
        // Position
        EditorGUILayout.LabelField($"Position: {building.position}");
        
        // Description
        EditorGUILayout.LabelField("Description:");
        string newDescription = EditorGUILayout.TextArea(editingDescription, GUILayout.Height(60));
        if (newDescription != editingDescription)
        {
            editingDescription = newDescription;
            buildingManager.SetBuildingDescription(building.id, newDescription);
        }
        
        EditorGUILayout.Space();
        
        // Action buttons
        EditorGUILayout.LabelField("Actions", EditorStyles.boldLabel);
        
        if (GUILayout.Button("Focus in Scene"))
        {
            Building buildingComponent = buildingManager.GetBuilding(building.id);
            if (buildingComponent != null)
            {
                Selection.activeGameObject = buildingComponent.gameObject;
                SceneView.FrameLastActiveSceneView();
            }
        }
        
        // Level modification controls
        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("First Level"))
        {
            buildingManager.SetBuildingLevel(building.id, 0);
        }
        if (GUILayout.Button("Last Level"))
        {
            buildingManager.SetBuildingLevel(building.id, building.maxLevel - 1);
        }
        EditorGUILayout.EndHorizontal();

        // Direct level input
        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField("Set Level:", GUILayout.Width(80));
        int targetLevel = EditorGUILayout.IntSlider(building.currentLevel + 1, 1, building.maxLevel) - 1;
        if (targetLevel != building.currentLevel)
        {
            buildingManager.SetBuildingLevel(building.id, targetLevel);
        }
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.Space();

        // Building state controls
        EditorGUILayout.LabelField("Building State", EditorStyles.boldLabel);

        EditorGUILayout.BeginHorizontal();
        bool isActive = building.isActive;
        bool newActiveState = EditorGUILayout.Toggle("Active", isActive);
        if (newActiveState != isActive)
        {
            Building buildingComponent = buildingManager.GetBuilding(building.id);
            if (buildingComponent != null)
            {
                buildingComponent.gameObject.SetActive(newActiveState);
                building.isActive = newActiveState;
                buildingManager.OnBuildingUpdated?.Invoke(building);
            }
        }
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.Space();

        GUI.backgroundColor = Color.red;
        if (GUILayout.Button("Delete Building"))
        {
            if (EditorUtility.DisplayDialog("Delete Building",
                $"Are you sure you want to delete '{building.buildingName}'?",
                "Delete", "Cancel"))
            {
                buildingManager.RemoveBuilding(building.id);
                selectedBuildingIndex = -1;
            }
        }
        GUI.backgroundColor = Color.white;
    }

    private void DrawBuildingCreationPanel()
    {
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("Create New Building", EditorStyles.boldLabel);

        if (GUILayout.Button("Create Building from Prefab"))
        {
            CreateBuildingFromPrefab();
        }

        if (GUILayout.Button("Create Empty Building"))
        {
            CreateEmptyBuilding();
        }
    }

    private void CreateBuildingFromPrefab()
    {
        string prefabPath = EditorUtility.OpenFilePanel("Select Building Prefab", "Assets", "prefab");
        if (!string.IsNullOrEmpty(prefabPath))
        {
            // Convert absolute path to relative path
            if (prefabPath.StartsWith(Application.dataPath))
            {
                prefabPath = "Assets" + prefabPath.Substring(Application.dataPath.Length);
            }

            GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
            if (prefab != null)
            {
                GameObject instance = PrefabUtility.InstantiatePrefab(prefab) as GameObject;
                if (instance != null)
                {
                    // Ensure it has a Building component
                    Building building = instance.GetComponent<Building>();
                    if (building == null)
                    {
                        building = instance.AddComponent<Building>();
                    }

                    // Parent to BuildingManager if it exists
                    if (buildingManager != null)
                    {
                        instance.transform.SetParent(buildingManager.transform);
                    }

                    // Refresh the building manager
                    buildingManager.RefreshBuildings();

                    // Select the new building
                    Selection.activeGameObject = instance;
                }
            }
        }
    }

    private void CreateEmptyBuilding()
    {
        GameObject newBuilding = new GameObject("New Building");
        Building building = newBuilding.AddComponent<Building>();

        // Initialize with default bank levels
        building.bankLevels = new BankLevel[1];
        building.bankLevels[0] = new BankLevel();
        building.bankLevels[0].gameObjects = new GameObject[0];

        // Parent to BuildingManager if it exists
        if (buildingManager != null)
        {
            newBuilding.transform.SetParent(buildingManager.transform);
        }

        // Refresh the building manager
        buildingManager.RefreshBuildings();

        // Select the new building
        Selection.activeGameObject = newBuilding;
    }
}
