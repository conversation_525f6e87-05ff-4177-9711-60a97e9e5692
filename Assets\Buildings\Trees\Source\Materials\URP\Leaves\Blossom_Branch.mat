%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-7830751558847854853
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: da692e001514ec24dbc4cca1949ff7e8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 12
  hdPluginSubTargetMaterialVersions:
    m_Keys: []
    m_Values: 
--- !u!114 &-6610574931659086465
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 9
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Blossom_Branch
  m_Shader: {fileID: -6465566751694194690, guid: 66ce69aa57b06a0459df37d174c1ad64, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords: []
  m_InvalidKeywords:
  - _DISABLE_SSR_TRANSPARENT
  - _DOUBLESIDED_ON
  - _ENABLE_FOG_ON_TRANSPARENT
  - _SURFACE_TYPE_TRANSPARENT
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 1
  m_CustomRenderQueue: -1
  stringTagMap:
    MotionVector: User
  disabledShaderPasses:
  - TransparentDepthPrepass
  - TransparentDepthPostpass
  - TransparentBackface
  - RayTracingPrepass
  - MOTIONVECTORS
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - Texture2D_499e86c557514ac39f046dd0fcd71716:
        m_Texture: {fileID: 2800000, guid: b9ba8a8ceb79275418fb46fb8b272d6a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - Texture2D_56976b58e68a4924af4d32fa432bba33:
        m_Texture: {fileID: 2800000, guid: b9ba8a8ceb79275418fb46fb8b272d6a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - Texture2D_8eb4f8eda9384dc0aa5203b2175bcd93:
        m_Texture: {fileID: 2800000, guid: 892db7f031af0be4988e2a1be2b47b1a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Normal_Map:
        m_Texture: {fileID: 2800000, guid: 892db7f031af0be4988e2a1be2b47b1a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - Vector1_07f7bfc681604ba8a1041aa20f0da95d: 0.62
    - Vector1_0fb587ac4ebf4295848ede2e10a1ea76: 1
    - Vector1_1d80f3fcf5df43dfbe7f3577893a8a7a: 0.2
    - Vector1_2366f028872548cca586a80231f9449e: 1
    - Vector1_7befe60a42be40dcaffc92d4b5e4e74e: 0.2
    - Vector1_97390a94d66b40a1a826d031b9c68e87: 1
    - Vector1_d69206dc4fba4596b4f269babfaf4cc1: 0.6
    - Vector1_f47f81ec635e453c8ca9794d4793a782: 0
    - _AddPrecomputedVelocity: 0
    - _AlphaCutoffEnable: 0
    - _AlphaDstBlend: 10
    - _AlphaSrcBlend: 1
    - _AlphaToMask: 0
    - _AlphaToMaskInspectorValue: 0
    - _BlendMode: 0
    - _ConservativeDepthOffsetEnable: 0
    - _CullMode: 0
    - _CullModeForward: 0
    - _DepthOffsetEnable: 0
    - _DoubleSidedEnable: 1
    - _DoubleSidedGIMode: 0
    - _DoubleSidedNormalMode: 2
    - _DstBlend: 10
    - _EnableBlendModePreserveSpecularLighting: 0
    - _EnableFogOnTransparent: 1
    - _Normal_Strength: 0.6
    - _OpaqueCullMode: 2
    - _QueueControl: 0
    - _QueueOffset: 0
    - _RayTracing: 0
    - _ReceivesSSR: 1
    - _ReceivesSSRTransparent: 0
    - _RefractionModel: 0
    - _RenderQueueType: 4
    - _RequireSplitLighting: 0
    - _Smoothness: 0
    - _SrcBlend: 1
    - _StencilRef: 0
    - _StencilRefDepth: 0
    - _StencilRefDistortionVec: 4
    - _StencilRefGBuffer: 2
    - _StencilRefMV: 32
    - _StencilWriteMask: 6
    - _StencilWriteMaskDepth: 8
    - _StencilWriteMaskDistortionVec: 4
    - _StencilWriteMaskGBuffer: 14
    - _StencilWriteMaskMV: 40
    - _SupportDecals: 1
    - _SurfaceType: 1
    - _TransparentBackfaceEnable: 0
    - _TransparentCullMode: 1
    - _TransparentDepthPostpassEnable: 0
    - _TransparentDepthPrepassEnable: 0
    - _TransparentSortPriority: 0
    - _TransparentWritingMotionVec: 0
    - _TransparentZWrite: 0
    - _UseShadowThreshold: 0
    - _ZTestDepthEqualForOpaque: 4
    - _ZTestGBuffer: 4
    - _ZTestTransparent: 4
    - _ZWrite: 0
    m_Colors:
    - Color_f81cfcc9b99a474895c54af03c1fefd0: {r: 1, g: 1, b: 1, a: 0}
    - Vector2_8e39cc9d9407448cb5fc78370e60216c: {r: 1, g: 0, b: 0, a: 0}
    - Vector2_ee3da453ee474862bd12028a3df74cd4: {r: 0, g: 0, b: 0, a: 0}
    - _Color: {r: 1, g: 1, b: 1, a: 0}
    - _DoubleSidedConstants: {r: 1, g: 1, b: 1, a: 0}
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
