using UnityEngine;
using UnityEditor;

[CustomEditor(typeof(Quest))]public class QuestEditor : Editor
{
    public bool showDetails = false;
    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();
        
        if (GUILayout.Button("Create Quest"))
        {
            GameObject go = new GameObject("Quest");
            go.AddComponent<Quest>();
            Selection.activeGameObject = go;
        }
    }

}