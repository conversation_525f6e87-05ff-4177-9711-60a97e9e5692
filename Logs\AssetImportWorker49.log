Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.47f1 (2ad1ed33fd3b) revision 2806253'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'fr' Physical Memory: 16051 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker49
-projectPath
C:/Users/<USER>/ONU
-logFile
Logs/AssetImportWorker49.log
-srvPort
56204
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: C:/Users/<USER>/ONU
C:/Users/<USER>/ONU
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [46296]  Target information:

Player connection [46296]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 3164868855 [EditorId] 3164868855 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-34E5P9N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [46296] Host joined multi-casting on [***********:54997]...
Player connection [46296] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 2.29 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.47f1 (2ad1ed33fd3b)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/ONU/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1650 (ID=0x1f9d)
    Vendor:   NVIDIA
    VRAM:     3937 MB
    Driver:   32.0.15.7602
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56896
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.002620 seconds.
- Loaded All Assemblies, in  0.324 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 214 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.519 seconds
Domain Reload Profiling: 841ms
	BeginReloadAssembly (111ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (41ms)
	LoadAllAssembliesAndSetupDomain (132ms)
		LoadAssemblies (107ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (129ms)
			TypeCache.Refresh (128ms)
				TypeCache.ScanAssembly (117ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (519ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (475ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (288ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (43ms)
			ProcessInitializeOnLoadAttributes (93ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.752 seconds
Refreshing native plugins compatible for Editor in 1.87 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mirror | mirror-networking.com | discord.gg/N9QVxbM
Mono: successfully reloaded assembly
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.859 seconds
Domain Reload Profiling: 1609ms
	BeginReloadAssembly (145ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (24ms)
	RebuildCommonClasses (40ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (36ms)
	LoadAllAssembliesAndSetupDomain (518ms)
		LoadAssemblies (342ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (264ms)
			TypeCache.Refresh (192ms)
				TypeCache.ScanAssembly (173ms)
			BuildScriptInfoCaches (53ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (860ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (710ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (109ms)
			ProcessInitializeOnLoadAttributes (344ms)
			ProcessInitializeOnLoadMethodAttributes (238ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 3.37 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 219 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7357 unused Assets / (7.4 MB). Loaded Objects now: 8019.
Memory consumption went from 188.9 MB to 181.5 MB.
Total: 15.822600 ms (FindLiveObjects: 0.866600 ms CreateObjectMapping: 1.248600 ms MarkObjects: 8.051600 ms  DeleteObjects: 5.652300 ms)

========================================================================
Received Import Request.
  Time since last request: 1840025.046641 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Armchair_01A.prefab
  artifactKey: Guid(744968bf9611ea74bb51c15ac2f8e60e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Armchair_01A.prefab using Guid(744968bf9611ea74bb51c15ac2f8e60e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '22f83d4c81223cc3e70fc4c8039e3900') in 0.602245 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Ceiling_Fixture_04B.prefab
  artifactKey: Guid(a3a192dd0531da441b49ee0560e2209d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Ceiling_Fixture_04B.prefab using Guid(a3a192dd0531da441b49ee0560e2209d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e2f90b1bde1074580d24b7e59204a382') in 0.0325246 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Dresser_02A.prefab
  artifactKey: Guid(029e2bb991be5844fb3657622c3955e3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Dresser_02A.prefab using Guid(029e2bb991be5844fb3657622c3955e3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '056543467a850998919d8d1ddd496644') in 0.0265645 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 17

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Chandelier_02A.prefab
  artifactKey: Guid(bf968ccca24e1b049a5861f9ea585e17) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Chandelier_02A.prefab using Guid(bf968ccca24e1b049a5861f9ea585e17) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd52cfe13ea9edf1fee068904b2fefec4') in 0.024695 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Bowl_02B.prefab
  artifactKey: Guid(54980f41d4984bc409ac32129fa6b7bf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Bowl_02B.prefab using Guid(54980f41d4984bc409ac32129fa6b7bf) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e54f76603ae0c36658b3817bd99b635c') in 0.0214973 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Coffee_Table_02A.prefab
  artifactKey: Guid(adbe0527d808ca44292374ec21bf0ed6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Coffee_Table_02A.prefab using Guid(adbe0527d808ca44292374ec21bf0ed6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ab4d7c517bcf76cdb086aeefb1b07bff') in 0.0302807 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Couch_01C.prefab
  artifactKey: Guid(9c58e81954fa89c4dbc6c8ffdfca84f1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Couch_01C.prefab using Guid(9c58e81954fa89c4dbc6c8ffdfca84f1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fcd4ca86e481a6fbdcd82119648191f6') in 0.0252457 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Bed_02A.prefab
  artifactKey: Guid(50dec950674b2ef44a738151b6c464c7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Bed_02A.prefab using Guid(50dec950674b2ef44a738151b6c464c7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd414e0e1d51f73b79d61416a20a326c1') in 0.0249838 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Bookshelf_01A.prefab
  artifactKey: Guid(6601ce380cb30594d84bcb8e3dd04903) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Bookshelf_01A.prefab using Guid(6601ce380cb30594d84bcb8e3dd04903) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5df067d12a8c1aa3d2a63fb3aa11aa99') in 0.0236331 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Bowl_01A.prefab
  artifactKey: Guid(82f6763425eb82d4db421e8c1fc8a2b1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Bowl_01A.prefab using Guid(82f6763425eb82d4db421e8c1fc8a2b1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'aa2695a16c7a1ee487669e91ba1488f0') in 0.0220932 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Fireplace_01B.prefab
  artifactKey: Guid(fbd658c7d1938fc4c90f0ad21c76b701) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Fireplace_01B.prefab using Guid(fbd658c7d1938fc4c90f0ad21c76b701) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '31e0cc3b971a70e627c6febfbd91a9b1') in 0.0257569 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Books_01B.prefab
  artifactKey: Guid(af3fd43a8f6085b439d7b7c830a938f7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Books_01B.prefab using Guid(af3fd43a8f6085b439d7b7c830a938f7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cfd1784850ca9b39602df479c2c74f6e') in 0.0231765 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Bowl_02C.prefab
  artifactKey: Guid(19a5c9b23e42a2241b4646f57aaeadcc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Bowl_02C.prefab using Guid(19a5c9b23e42a2241b4646f57aaeadcc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e4b27418e1b42ca8896eb26ed709f587') in 0.0356882 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Couch_01B.prefab
  artifactKey: Guid(40d01dee940315c4db2e8b106f81987f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Couch_01B.prefab using Guid(40d01dee940315c4db2e8b106f81987f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c3c72c67a1df003dddd827dd53cae9f1') in 0.0231518 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Book_01A.prefab
  artifactKey: Guid(cc38b5aa14f8d0940abc3a2572f5e55b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Book_01A.prefab using Guid(cc38b5aa14f8d0940abc3a2572f5e55b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cb4e220b9c9d14e89003784128300923') in 0.0251615 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Couch_01A.prefab
  artifactKey: Guid(2d8e6f66ce528734288f16d37db80dde) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Couch_01A.prefab using Guid(2d8e6f66ce528734288f16d37db80dde) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '66d22978a4d62071a625279dca76ed49') in 0.0212427 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Bowl_02A.prefab
  artifactKey: Guid(fd56caffe3a65c1498e88ccff6bf4bdc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Bowl_02A.prefab using Guid(fd56caffe3a65c1498e88ccff6bf4bdc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '244afe794ec2b8e0315d73c731330988') in 0.0211844 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Armchair_02B.prefab
  artifactKey: Guid(67731e6ad1e01654999b230a256f9755) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Armchair_02B.prefab using Guid(67731e6ad1e01654999b230a256f9755) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f4a1327082dbc4b5804b53a400a4d121') in 0.0231695 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Armchair_02C.prefab
  artifactKey: Guid(3f9e2d17623d13d4d97a829321fe78b4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Armchair_02C.prefab using Guid(3f9e2d17623d13d4d97a829321fe78b4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '665ee608ca0a073d90185770e56b7ca2') in 0.023584 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Coffee_Table_01B.prefab
  artifactKey: Guid(897e0e163937075499c45ff4bfed99c3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Coffee_Table_01B.prefab using Guid(897e0e163937075499c45ff4bfed99c3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b7ec9d46300f06c7fa1a519e3162f8b6') in 0.0280102 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Ceiling_Fixture_04A.prefab
  artifactKey: Guid(2922da53bfe9b0343b2ef6c0f1c82657) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Ceiling_Fixture_04A.prefab using Guid(2922da53bfe9b0343b2ef6c0f1c82657) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3c03a9084a2f58057500b1a02e063b7a') in 0.0260398 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Couch_02B.prefab
  artifactKey: Guid(527b4d58b31c5594a9affbcb05bd7e3f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Couch_02B.prefab using Guid(527b4d58b31c5594a9affbcb05bd7e3f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f26f66d41c53f4b295b1ad94d96e8000') in 0.0229258 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Bowl_01C.prefab
  artifactKey: Guid(abaf8b1bd8b5e7843bfcbcf13fdb4efa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Bowl_01C.prefab using Guid(abaf8b1bd8b5e7843bfcbcf13fdb4efa) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '372d4cab88909251c9db100e470db682') in 0.0209685 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Chair_Desk_01A.prefab
  artifactKey: Guid(238674eee31e02b41bce328ed042813f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Chair_Desk_01A.prefab using Guid(238674eee31e02b41bce328ed042813f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4f307d0eb0c8d7462dd10573cf24189f') in 0.0246518 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Couch_02A.prefab
  artifactKey: Guid(edaa09dad2e0bfc4788f922e8f0a673a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Couch_02A.prefab using Guid(edaa09dad2e0bfc4788f922e8f0a673a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '803b8af51759370b095ea101372f57bc') in 0.0226022 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 1.661533 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Lamp_01A.prefab
  artifactKey: Guid(b34503ab84cb69c42b245dcdf0e2dd78) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Lamp_01A.prefab using Guid(b34503ab84cb69c42b245dcdf0e2dd78) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ddb67e54e5510bb77c1e33ee5a989933') in 0.0287835 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Nightstand_03A.prefab
  artifactKey: Guid(2206c5b55d8d66d46bc4f008f0214fde) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Nightstand_03A.prefab using Guid(2206c5b55d8d66d46bc4f008f0214fde) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6cbdb20d5b4783a8300d6986b7c84413') in 0.0291247 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 17

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Ottoman_01A.prefab
  artifactKey: Guid(836b24e305791344496f952c80e575ec) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Ottoman_01A.prefab using Guid(836b24e305791344496f952c80e575ec) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5908c61d48b8135319109333ad09a8f7') in 0.0216842 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Newspaper_01A.prefab
  artifactKey: Guid(2e9b640a5c1e81b41a7442f9131a868e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Newspaper_01A.prefab using Guid(2e9b640a5c1e81b41a7442f9131a868e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd4f6ef06205fd60028d607dce1dda14c') in 0.0208002 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Nightstand_01A.prefab
  artifactKey: Guid(5356dc025e738254caee30c51fa0167f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Nightstand_01A.prefab using Guid(5356dc025e738254caee30c51fa0167f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '28f7e420af40e40c9ec0f50432ceda47') in 0.0234777 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Lounge_Chair_01B.prefab
  artifactKey: Guid(beab3e210e45bef4790accf0e8a69f38) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Lounge_Chair_01B.prefab using Guid(beab3e210e45bef4790accf0e8a69f38) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '629cb7cf3581d218720b04b3aa42392c') in 0.0214263 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Lounge_Chair_01A.prefab
  artifactKey: Guid(ceac81ee76fb2b74ab3914dd3ac0d50d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Lounge_Chair_01A.prefab using Guid(ceac81ee76fb2b74ab3914dd3ac0d50d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bcb2ecdea59fe4b7e7976578d6d75bf7') in 0.0222683 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Nightstand_01C.prefab
  artifactKey: Guid(e97bd8d0eabe22a44bbbf748b9738c2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Nightstand_01C.prefab using Guid(e97bd8d0eabe22a44bbbf748b9738c2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1b6ec0b884d088d399faeb0a1a5f28c5') in 0.0191494 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 266.891800 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_PC_01A.prefab
  artifactKey: Guid(29404bd258ba7c54ebf1bce81efe0c48) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_PC_01A.prefab using Guid(29404bd258ba7c54ebf1bce81efe0c48) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a47c1031280d71d8bae1f93857891d97') in 0.0290578 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Pillow_01A.prefab
  artifactKey: Guid(0454598b668546747b40fc20a3afca2b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Pillow_01A.prefab using Guid(0454598b668546747b40fc20a3afca2b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5036f20231f46ad62dbfee962cbaa462') in 0.0425897 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Pillow_01B.prefab
  artifactKey: Guid(2189d282afc1e0842821564f173cc772) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Pillow_01B.prefab using Guid(2189d282afc1e0842821564f173cc772) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '42fca47aa70ad754d020c7f0a64bcbf5') in 0.0384756 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_PC_Laptop_01A.prefab
  artifactKey: Guid(64cd845a5a80aa749821c5bb565b39fe) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_PC_Laptop_01A.prefab using Guid(64cd845a5a80aa749821c5bb565b39fe) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4714031fd7426e4dbc7750ec9b0b25d7') in 0.0223315 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.019060 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Pillow_01D.prefab
  artifactKey: Guid(33126813c7df2564ca381e87c5043f16) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Pillow_01D.prefab using Guid(33126813c7df2564ca381e87c5043f16) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0f2484215d3976867ef4d5cf7207a1fd') in 0.0348681 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Pillow_01H.prefab
  artifactKey: Guid(683d9b308eaa97f45947d78fc1e475e7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Pillow_01H.prefab using Guid(683d9b308eaa97f45947d78fc1e475e7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd193fd08baf31ccb2be17c27c8d2fc2f') in 0.0217867 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Pillow_01F.prefab
  artifactKey: Guid(972ad661686c77341a16b879125999cf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Pillow_01F.prefab using Guid(972ad661686c77341a16b879125999cf) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1a683f62dc53b99e84e3ee849b2303cb') in 0.020895 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_01A.prefab
  artifactKey: Guid(9ce0265774f6e4a4f8435da6e8e793d5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_01A.prefab using Guid(9ce0265774f6e4a4f8435da6e8e793d5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c45b28c1c52433228302054d3ad8ba62') in 0.0241112 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.164970 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_01B.prefab
  artifactKey: Guid(083a7f54e13bc9d4fab2545a00e71678) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_01B.prefab using Guid(083a7f54e13bc9d4fab2545a00e71678) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e5ea06de3036fc8033812931476e38d7') in 0.024389 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_02B.prefab
  artifactKey: Guid(11124ae3c6daecc4aa835ba101dd77bf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_02B.prefab using Guid(11124ae3c6daecc4aa835ba101dd77bf) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e61adebaf1f3a52a710ecd4983af00da') in 0.0235917 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_02C.prefab
  artifactKey: Guid(8f30ae520f6eea84bb8678cc4ba3c22a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_02C.prefab using Guid(8f30ae520f6eea84bb8678cc4ba3c22a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0a6c129002df11dd40f581de3ea4d93b') in 0.0200994 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_02A.prefab
  artifactKey: Guid(43da608b2c105f5499e65692622e7f8b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_02A.prefab using Guid(43da608b2c105f5499e65692622e7f8b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '33f966186332b8ab17ad82e472915ad3') in 0.023474 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorkerClient::OnTransportError - code=2 error=End of file
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0