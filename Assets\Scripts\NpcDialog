using UnityEngine;
using UnityEngine.UI;

public class NpcDialog : MonoBehaviour
{
    [Header("Dialog Settings")]
    public Text dialogText;
    public string[] dialogLines;
    private int currentLine = 0;

    [Header("Audio Settings")]
    public bool enableAudio = true;
    public AudioSource audioSource;
    public AudioClip dialogSound;

    [Head<PERSON>("Prefab Settings")]
    public GameObject prefabToInstantiate;

    void Start()
    {
        ShowDialog();
    }

    void Update()
    {
        if (Input.GetKeyDown(KeyCode.Space))
        {
            NextLine();
        }
    }

    void ShowDialog()
    {
        if (currentLine < dialogLines.Length)
        {
            dialogText.text = dialogLines[currentLine];
            PlayDialogSound();
        }
    }

    public void NextLine()
    {
        currentLine++;
        if (currentLine < dialogLines.Length)
        {
            ShowDialog();
        }
        else
        {
            EndDialog();
        }
    }

    void EndDialog()
    {
        Destroy(gameObject);
    }

    /// <summary>
    /// Plays dialog sound if audio is enabled and components are properly set up
    /// </summary>
    void PlayDialogSound()
    {
        if (enableAudio && audioSource != null && dialogSound != null)
        {
            audioSource.PlayOneShot(dialogSound);
        }
    }

    /// <summary>
    /// Instantiates the public prefab at the current transform position
    /// </summary>
    public void InstantiatePrefab()
    {
        if (prefabToInstantiate != null)
        {
            Instantiate(prefabToInstantiate, transform.position, transform.rotation);
        }
        else
        {
            Debug.LogWarning("No prefab assigned to instantiate!");
        }
    }

    /// <summary>
    /// Instantiates the public prefab at a specified position
    /// </summary>
    /// <param name="position">Position where to instantiate the prefab</param>
    public void InstantiatePrefab(Vector3 position)
    {
        if (prefabToInstantiate != null)
        {
            Instantiate(prefabToInstantiate, position, transform.rotation);
        }
        else
        {
            Debug.LogWarning("No prefab assigned to instantiate!");
        }
    }

    /// <summary>
    /// Instantiates the public prefab at a specified position and rotation
    /// </summary>
    /// <param name="position">Position where to instantiate the prefab</param>
    /// <param name="rotation">Rotation for the instantiated prefab</param>
    public void InstantiatePrefab(Vector3 position, Quaternion rotation)
    {
        if (prefabToInstantiate != null)
        {
            Instantiate(prefabToInstantiate, position, rotation);
        }
        else
        {
            Debug.LogWarning("No prefab assigned to instantiate!");
        }
    }
}