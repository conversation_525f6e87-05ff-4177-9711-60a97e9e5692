using UnityEngine;
using UnityEditor;

[CustomEditor(typeof(TasksManager))]public class TasksManagerEditor : Editor
{
    // Variables for task creation
    private string newTaskName = "New Task";
    private TaskType newTaskType = TaskType.Quiz;
    private int newTaskBuildingIndex = 0;
    private Vector3 newTaskPosition = Vector3.zero;

    // Foldout states for collapsible sections
    private bool showAddNewTask = true;
    private bool showTasks = true;
    private bool showManageTypes = false;

    // Variables for type management
    private string newTypeName = "";

    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();

        TasksManager tasksManager = (TasksManager)target;

        if (GUILayout.Button("Reload Tasks"))
        {
            tasksManager.LoadDataFromChildren();
        }

        if (GUILayout.Button("Reset Task Done"))
        {
            Tasks.taskCount = 0;
        }

        GUILayout.Label("Task Done: " + Tasks.taskCount);

        // Get building manager for dropdown options (used by multiple sections)
        BuildingManager buildingManager = FindFirstObjectByType<BuildingManager>();
        string[] buildingNames = new string[0];

        if (buildingManager != null)
        {
            var buildingDataList = buildingManager.GetAllBuildingData();
            buildingNames = new string[buildingDataList.Count];
            for (int i = 1; i < buildingDataList.Count; i++)
            {
                buildingNames[i] = buildingDataList[i].buildingName;
            }
        }

        // Add Task Creation Section
        GUILayout.Space(10);
        showAddNewTask = EditorGUILayout.Foldout(showAddNewTask, "Add New Task", true, EditorStyles.foldoutHeader);

        if (showAddNewTask)
        {

            EditorGUILayout.BeginVertical("box");

        // Task name input
        EditorGUILayout.BeginHorizontal();
        GUILayout.Label("Task Name:", GUILayout.Width(80));
        newTaskName = EditorGUILayout.TextField(newTaskName);
        EditorGUILayout.EndHorizontal();

        // Task type selection
        EditorGUILayout.BeginHorizontal();
        GUILayout.Label("Task Type:", GUILayout.Width(80));
        newTaskType = (TaskType)EditorGUILayout.EnumPopup(newTaskType);
        EditorGUILayout.EndHorizontal();

        // Building selection
        if (buildingNames.Length > 0)
        {
            EditorGUILayout.BeginHorizontal();
            GUILayout.Label("Building:", GUILayout.Width(80));
            newTaskBuildingIndex = EditorGUILayout.Popup(newTaskBuildingIndex, buildingNames);
            EditorGUILayout.EndHorizontal();
        }
        else
        {
            EditorGUILayout.HelpBox("No buildings found. Make sure BuildingManager exists in the scene.", MessageType.Warning);
        }

        GUILayout.Space(5);

        // Add Task button
        GUI.enabled = !string.IsNullOrEmpty(newTaskName) && buildingNames.Length > 0;
        if (GUILayout.Button("Add Task", GUILayout.Height(25)))
        {
            newTaskPosition = buildingManager.buildings[newTaskBuildingIndex].transform.position;
            CreateNewTask(tasksManager, newTaskName, newTaskType, newTaskBuildingIndex, newTaskPosition);
        }
        GUI.enabled = true;

            EditorGUILayout.EndVertical();
        }

        // Display tasks in a tab-like interface
        if (tasksManager.tasks != null && tasksManager.tasks.Length > 0)
        {
            GUILayout.Space(10);
            showTasks = EditorGUILayout.Foldout(showTasks, "Tasks:", true, EditorStyles.foldoutHeader);

            if (showTasks)
            {

            // Display each task in a tab-like format
            for (int i = 0; i < tasksManager.tasks.Length; i++)
            {
                Tasks task = tasksManager.tasks[i];
                if (task == null) continue;

                // Create a box for each task (tab-like appearance)
                EditorGUILayout.BeginVertical("box");

                // Task header with name
                EditorGUILayout.BeginHorizontal();
                GUILayout.Label($"Task {i + 1}: {task.name}", EditorStyles.boldLabel);
                GUILayout.FlexibleSpace();
                GUILayout.Label($"Type:", GUILayout.Width(35));
                TaskType newTaskType = (TaskType)EditorGUILayout.EnumPopup(task.taskType, GUILayout.Width(80));

                if (newTaskType != task.taskType)
                {
                    Undo.RecordObject(task, "Change Task Type");
                    task.taskType = newTaskType;
                    EditorUtility.SetDirty(task);
                }
                EditorGUILayout.EndHorizontal();

                // Task controls row
                EditorGUILayout.BeginHorizontal();

                // Building selection dropdown
                if (buildingNames.Length > 0)
                {
                    GUILayout.Label("Building:", GUILayout.Width(60));
                    int currentBuildingIndex = Mathf.Clamp(task.buildingIndex, 0, buildingNames.Length - 1);
                    int newBuildingIndex = EditorGUILayout.Popup(currentBuildingIndex, buildingNames, GUILayout.Width(120));

                    if (newBuildingIndex != task.buildingIndex)
                    {
                        Undo.RecordObject(task, "Change Task Building");
                        task.buildingIndex = newBuildingIndex;
                        EditorUtility.SetDirty(task);
                    }
                }
                else
                {
                    GUILayout.Label("No buildings found", GUILayout.Width(120));
                }

                GUILayout.FlexibleSpace();

                // Task action buttons
                if (GUILayout.Button("Complete", GUILayout.Width(70)))
                {
                    if (EditorUtility.DisplayDialog("Complete Task",
                        $"Are you sure you want to complete task '{task.name}'?",
                        "Yes", "No"))
                    {
                        task.CompleteTask();
                        tasksManager.LoadDataFromChildren(); // Refresh the task list
                    }
                }

                if (GUILayout.Button("Delete", GUILayout.Width(60)))
                {
                    if (EditorUtility.DisplayDialog("Delete Task",
                        $"Are you sure you want to delete task '{task.name}'?",
                        "Yes", "No"))
                    {
                        Undo.DestroyObjectImmediate(task.gameObject);
                        tasksManager.LoadDataFromChildren(); // Refresh the task list
                    }
                }

                EditorGUILayout.EndHorizontal();

                // Additional task info
                EditorGUILayout.BeginHorizontal();
                GUILayout.Label($"Building Index: {task.buildingIndex}", EditorStyles.miniLabel);
                GUILayout.FlexibleSpace();
                GUILayout.Label($"Position: {task.transform.position}", EditorStyles.miniLabel);
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.EndVertical();
                GUILayout.Space(5);
            }
            }
        }
        else
        {
            GUILayout.Space(10);
            EditorGUILayout.HelpBox("No tasks found. Make sure tasks are children of this TasksManager.", MessageType.Info);
        }

        // Manage Types Section
        GUILayout.Space(10);
        showManageTypes = EditorGUILayout.Foldout(showManageTypes, "Manage Types", true, EditorStyles.foldoutHeader);

        if (showManageTypes)
        {
            EditorGUILayout.BeginVertical("box");

            // Display current types
            GUILayout.Label("Current Task Types:", EditorStyles.boldLabel);

            string[] enumNames = System.Enum.GetNames(typeof(TaskType));
            for (int i = 0; i < enumNames.Length; i++)
            {
                EditorGUILayout.BeginHorizontal();
                GUILayout.Label($"{i + 1}. {enumNames[i]}", GUILayout.ExpandWidth(true));

                if (GUILayout.Button("Delete", GUILayout.Width(60)))
                {
                    if (EditorUtility.DisplayDialog("Delete Task Type",
                        $"Are you sure you want to delete task type '{enumNames[i]}'?\n\nThis will automatically remove it from the enum and delete the script file.",
                        "Yes", "No"))
                    {
                        DeleteTaskType(enumNames[i]);
                    }
                }
                EditorGUILayout.EndHorizontal();
            }

            GUILayout.Space(10);

            // Add new type section
            GUILayout.Label("Add New Task Type:", EditorStyles.boldLabel);
            EditorGUILayout.BeginHorizontal();
            GUILayout.Label("Type Name:", GUILayout.Width(80));
            newTypeName = EditorGUILayout.TextField(newTypeName);
            EditorGUILayout.EndHorizontal();

            GUILayout.Space(5);

            GUI.enabled = !string.IsNullOrEmpty(newTypeName) && !string.IsNullOrWhiteSpace(newTypeName);
            if (GUILayout.Button("Add Task Type", GUILayout.Height(25)))
            {
                AddNewTaskType(newTypeName.Trim());
            }
            GUI.enabled = true;

            EditorGUILayout.EndVertical();
        }
    }

    /// <summary>
    /// Creates a new task as a child of the TasksManager
    /// </summary>
    private void CreateNewTask(TasksManager tasksManager, string taskName, TaskType taskType, int buildingIndex, Vector3 position)
    {
        // Create new GameObject for the task
        GameObject taskObject = new GameObject(taskName);

        // Set it as a child of the TasksManager
        taskObject.transform.SetParent(tasksManager.transform);
        taskObject.transform.position = position;

        // Add the Tasks component
        Tasks taskComponent = taskObject.AddComponent<Tasks>();

        // Set task properties
        taskComponent.taskType = taskType;
        taskComponent.buildingIndex = Mathf.Clamp(buildingIndex, 0, int.MaxValue);

        // Register undo operation
        Undo.RegisterCreatedObjectUndo(taskObject, "Create New Task");

        // Mark the TasksManager as dirty to ensure changes are saved
        EditorUtility.SetDirty(tasksManager);

        // Refresh the task list
        tasksManager.LoadDataFromChildren();

        // Reset the input fields for next task
        newTaskName = "New Task";
        newTaskType = TaskType.Quiz;
        newTaskBuildingIndex = 0;
        newTaskPosition = Vector3.zero;

        Debug.Log($"Created new task: {taskName} with type {taskType} at position {position}");
    }

    /// <summary>
    /// Adds a new task type by modifying the TaskType enum and creating the corresponding script
    /// </summary>
    private void AddNewTaskType(string typeName)
    {
        // Validate the type name
        if (string.IsNullOrEmpty(typeName) || !System.Text.RegularExpressions.Regex.IsMatch(typeName, @"^[A-Za-z][A-Za-z0-9_]*$"))
        {
            EditorUtility.DisplayDialog("Invalid Type Name",
                "Type name must start with a letter and contain only letters, numbers, and underscores.",
                "OK");
            return;
        }

        // Check if type already exists
        if (System.Enum.IsDefined(typeof(TaskType), typeName))
        {
            EditorUtility.DisplayDialog("Type Already Exists",
                $"Task type '{typeName}' already exists.",
                "OK");
            return;
        }

        // Automatically add the new type to the enum
        if (AddTypeToEnum(typeName))
        {
            // Create the script file
            CreateTaskTypeScript(typeName);

            // Show success message
            EditorUtility.DisplayDialog("Task Type Added",
                $"Successfully added '{typeName}' to TaskType enum and created the script file.\n\n" +
                "The changes will take effect after Unity recompiles the scripts.",
                "OK");
        }
        else
        {
            EditorUtility.DisplayDialog("Error",
                "Failed to add the task type. Please check the console for details.",
                "OK");
            return;
        }

        // Reset the input field
        newTypeName = "";

        Debug.Log($"Instructions provided for adding new task type: {typeName}");
    }

    /// <summary>
    /// Creates a new task type script file
    /// </summary>
    private void CreateTaskTypeScript(string typeName)
    {
        string tasksDirectoryPath = "Assets/Tasks";
        if (!System.IO.Directory.Exists(tasksDirectoryPath))
        {
            System.IO.Directory.CreateDirectory(tasksDirectoryPath);
            AssetDatabase.Refresh();
        }

        string scriptPath = $"{tasksDirectoryPath}/{typeName}.cs";

        if (System.IO.File.Exists(scriptPath))
        {
            Debug.LogWarning($"Script file already exists: {scriptPath}");
            return;
        }

        string scriptContent = $@"using UnityEngine;

public class {typeName} : MonoBehaviour
{{
    // Start is called before the first frame update
    void Start()
    {{

    }}

    // Update is called once per frame
    void Update()
    {{

    }}
}}";

        System.IO.File.WriteAllText(scriptPath, scriptContent);
        AssetDatabase.Refresh();

        Debug.Log($"Created script file: {scriptPath}");
    }

    /// <summary>
    /// Automatically adds a new type to the TaskType enum in Tasks.cs
    /// </summary>
    private bool AddTypeToEnum(string typeName)
    {
        string tasksFilePath = "Assets/Tasks.cs";

        if (!System.IO.File.Exists(tasksFilePath))
        {
            Debug.LogError($"Tasks.cs file not found at {tasksFilePath}");
            return false;
        }

        try
        {
            // Read the current file content
            string fileContent = System.IO.File.ReadAllText(tasksFilePath);

            // Find the TaskType enum
            string enumPattern = @"public enum TaskType\s*\{([^}]*)\}";
            System.Text.RegularExpressions.Match enumMatch = System.Text.RegularExpressions.Regex.Match(fileContent, enumPattern);

            if (!enumMatch.Success)
            {
                Debug.LogError("Could not find TaskType enum in Tasks.cs");
                return false;
            }

            // Get the enum content
            string enumContent = enumMatch.Groups[1].Value;

            // Check if the type already exists (case-insensitive)
            if (enumContent.ToLower().Contains(typeName.ToLower()))
            {
                Debug.LogWarning($"Task type '{typeName}' already exists in the enum");
                return false;
            }

            // Find the last enum value and add the new one
            string[] lines = enumContent.Split('\n');
            System.Text.StringBuilder newEnumContent = new System.Text.StringBuilder();

            // First, find the last non-empty line that contains an enum value
            int lastEnumLineIndex = -1;
            for (int i = lines.Length - 1; i >= 0; i--)
            {
                string line = lines[i].Trim();
                if (!string.IsNullOrWhiteSpace(line))
                {
                    lastEnumLineIndex = i;
                    break;
                }
            }

            // Now rebuild the enum content
            for (int i = 0; i < lines.Length; i++)
            {
                if (i == lastEnumLineIndex)
                {
                    // This is the last enum value line
                    string line = lines[i].Trim();

                    // Add comma to the last existing enum value if it doesn't have one
                    if (!line.EndsWith(",") && !line.EndsWith(",\r"))
                    {
                        // Preserve original indentation
                        string originalLine = lines[i];
                        string indentation = originalLine.Substring(0, originalLine.Length - originalLine.TrimStart().Length);
                        newEnumContent.AppendLine(indentation + line + ",");
                    }
                    else
                    {
                        newEnumContent.AppendLine(lines[i]);
                    }

                    // Add the new enum value with same indentation as the last one
                    string lastLineIndentation = lines[i].Substring(0, lines[i].Length - lines[i].TrimStart().Length);
                    newEnumContent.AppendLine($"{lastLineIndentation}{typeName}");
                }
                else
                {
                    newEnumContent.AppendLine(lines[i]);
                }
            }

            // Replace the enum content in the file
            string newFileContent = fileContent.Replace(enumMatch.Groups[1].Value, newEnumContent.ToString());

            // Write the updated content back to the file
            System.IO.File.WriteAllText(tasksFilePath, newFileContent);

            // Refresh the asset database to trigger recompilation
            AssetDatabase.Refresh();

            Debug.Log($"Successfully added '{typeName}' to TaskType enum");
            return true;
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"Error adding type to enum: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Automatically deletes a task type from the enum and removes the script file
    /// </summary>
    private void DeleteTaskType(string typeName)
    {
        bool enumUpdated = RemoveTypeFromEnum(typeName);
        bool scriptDeleted = DeleteTaskTypeScript(typeName);

        if (enumUpdated && scriptDeleted)
        {
            EditorUtility.DisplayDialog("Task Type Deleted",
                $"Successfully removed '{typeName}' from TaskType enum and deleted the script file.\n\n" +
                "The changes will take effect after Unity recompiles the scripts.",
                "OK");
        }
        else if (enumUpdated && !scriptDeleted)
        {
            EditorUtility.DisplayDialog("Partial Success",
                $"Removed '{typeName}' from TaskType enum, but could not delete the script file.\n\n" +
                "Please manually delete Assets/Tasks/{typeName}.cs if it exists.",
                "OK");
        }
        else if (!enumUpdated && scriptDeleted)
        {
            EditorUtility.DisplayDialog("Partial Success",
                $"Deleted the script file, but could not remove '{typeName}' from TaskType enum.\n\n" +
                "Please check the console for details.",
                "OK");
        }
        else
        {
            EditorUtility.DisplayDialog("Error",
                "Failed to delete the task type. Please check the console for details.",
                "OK");
        }
    }

    /// <summary>
    /// Removes a type from the TaskType enum in Tasks.cs
    /// </summary>
    private bool RemoveTypeFromEnum(string typeName)
    {
        string tasksFilePath = "Assets/Tasks.cs";

        if (!System.IO.File.Exists(tasksFilePath))
        {
            Debug.LogError($"Tasks.cs file not found at {tasksFilePath}");
            return false;
        }

        try
        {
            // Read the current file content
            string fileContent = System.IO.File.ReadAllText(tasksFilePath);

            // Find the TaskType enum
            string enumPattern = @"public enum TaskType\s*\{([^}]*)\}";
            System.Text.RegularExpressions.Match enumMatch = System.Text.RegularExpressions.Regex.Match(fileContent, enumPattern);

            if (!enumMatch.Success)
            {
                Debug.LogError("Could not find TaskType enum in Tasks.cs");
                return false;
            }

            // Get the enum content
            string enumContent = enumMatch.Groups[1].Value;

            // Remove the specified type
            string[] lines = enumContent.Split('\n');
            System.Text.StringBuilder newEnumContent = new System.Text.StringBuilder();

            bool typeFound = false;
            int typeLineIndex = -1;

            // First pass: find the line with the type to remove
            for (int i = 0; i < lines.Length; i++)
            {
                string line = lines[i].Trim();
                if (!string.IsNullOrWhiteSpace(line))
                {
                    string cleanLine = line.Replace(",", "").Trim();
                    if (cleanLine.Equals(typeName, System.StringComparison.OrdinalIgnoreCase))
                    {
                        typeFound = true;
                        typeLineIndex = i;
                        break;
                    }
                }
            }

            if (!typeFound)
            {
                Debug.LogWarning($"Task type '{typeName}' not found in the enum");
                return false;
            }

            // Find if this is the last enum value (to handle comma properly)
            bool isLastEnumValue = true;
            for (int i = typeLineIndex + 1; i < lines.Length; i++)
            {
                string line = lines[i].Trim();
                if (!string.IsNullOrWhiteSpace(line))
                {
                    isLastEnumValue = false;
                    break;
                }
            }

            // Second pass: rebuild the content without the target type
            for (int i = 0; i < lines.Length; i++)
            {
                if (i == typeLineIndex)
                {
                    // Skip the line with the type to remove
                    continue;
                }

                // If we removed the last enum value, we need to remove the comma from the new last value
                if (isLastEnumValue && i == typeLineIndex - 1)
                {
                    string line = lines[i];
                    if (line.TrimEnd().EndsWith(","))
                    {
                        // Remove the trailing comma
                        line = line.TrimEnd().TrimEnd(',');
                        newEnumContent.AppendLine(line);
                    }
                    else
                    {
                        newEnumContent.AppendLine(lines[i]);
                    }
                }
                else
                {
                    newEnumContent.AppendLine(lines[i]);
                }
            }

            // Replace the enum content in the file
            string newFileContent = fileContent.Replace(enumMatch.Groups[1].Value, newEnumContent.ToString());

            // Write the updated content back to the file
            System.IO.File.WriteAllText(tasksFilePath, newFileContent);

            // Refresh the asset database to trigger recompilation
            AssetDatabase.Refresh();

            Debug.Log($"Successfully removed '{typeName}' from TaskType enum");
            return true;
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"Error removing type from enum: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Deletes the script file for a task type
    /// </summary>
    private bool DeleteTaskTypeScript(string typeName)
    {
        string scriptPath = $"Assets/Tasks/{typeName}.cs";

        if (!System.IO.File.Exists(scriptPath))
        {
            Debug.LogWarning($"Script file not found: {scriptPath}");
            return true; // Consider this a success since the file doesn't exist
        }

        try
        {
            AssetDatabase.DeleteAsset(scriptPath);
            AssetDatabase.Refresh();

            Debug.Log($"Successfully deleted script file: {scriptPath}");
            return true;
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"Error deleting script file: {ex.Message}");
            return false;
        }
    }
}