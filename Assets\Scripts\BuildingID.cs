using UnityEngine;

/// <summary>
/// Component to store a unique identifier for buildings
/// </summary>
public class BuildingID : MonoBehaviour
{
    [SerializeField]
    public string id;

    void Awake()
    {
        // Generate ID if not set
        if (string.IsNullOrEmpty(id))
        {
            id = System.Guid.NewGuid().ToString();
        }
    }

    /// <summary>
    /// Regenerate the ID (useful for duplicated objects)
    /// </summary>
    public void RegenerateID()
    {
        id = System.Guid.NewGuid().ToString();
    }
}
